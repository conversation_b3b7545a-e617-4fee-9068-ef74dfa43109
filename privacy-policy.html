<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Privacy Policy - AI LABS</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="static/css/style.css" rel="stylesheet">
</head>
<body class="ai-gradient-bg font-inter">
    <!-- WebGL Shader Background -->
    <div class="shader-background"></div>
    
    <!-- Abstract Shapes -->
    <div class="abstract-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
    </div>

    <!-- Modern Navbar -->
    <nav class="modern-nav fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-24">
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center nav-brand">
                        <img src="static/images/ailabs.png" 
                             alt="AI LABS Logo" 
                             class="w-48 h-16 object-contain">
                    </a>
                </div>
                <!-- Dark Mode Switch -->
                <div class="flex items-center">
                    <button id="darkModeToggle" class="p-2 rounded-lg hover:bg-white/10 transition-colors duration-200">
                        <!-- Sun icon for light mode -->
                        <svg id="lightIcon" class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
                        </svg>
                        <!-- Moon icon for dark mode (hidden by default) -->
                        <svg id="darkIcon" class="hidden w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Privacy Policy Content -->
    <div class="pt-32 pb-8 md:pt-40 px-4 content-overlay">
        <div class="max-w-3xl mx-auto">
            <header class="text-center mb-12">
                <h1 class="text-3xl md:text-4xl font-bold mb-6 text-white">Privacy Policy for AI Executive Summary Chrome Extension</h1>
                <p class="text-white/70">Last updated: October 27, 2024</p>
            </header>

            <div class="page-content">
                <section class="mb-8">
                    <h2 class="text-2xl font-semibold text-[#2B3990] mb-4">1. Introduction</h2>
                    <p class="text-gray-700 mb-4">
                        This privacy policy explains how our AI Executive Summary Chrome Extension ("the Extension") collects, uses, and protects user data.
                    </p>
                </section>

                <section class="mb-8">
                    <h2 class="text-2xl font-semibold text-[#2B3990] mb-4">2. Data Collection and Use</h2>
                    <p class="text-gray-700 mb-4">
                        Our Extension collects and processes the following data:
                    </p>
                    <ul class="list-disc pl-6 text-gray-700 mb-4">
                        <li>Website Content: The Extension reads the content of the active webpage to generate summaries. This content is processed locally and sent to OpenAI's API for summarization.</li>
                        <li>Web History: The Extension accesses the URL of the current webpage to ensure it can summarize the correct content.</li>
                        <li>OpenAI API Key: Users provide their OpenAI API key, which is stored locally on their device using Chrome's storage API.</li>
                    </ul>
                </section>

                <section class="mb-8">
                    <h2 class="text-2xl font-semibold text-[#2B3990] mb-4">3. Data Sharing</h2>
                    <p class="text-gray-700 mb-4">
                        The only third party that receives any data is OpenAI, which receives the webpage content for summarization purposes. We do not sell, rent, or share any user data with any other third parties.
                    </p>
                </section>

                <section class="mb-8">
                    <h2 class="text-2xl font-semibold text-[#2B3990] mb-4">4. Data Storage</h2>
                    <p class="text-gray-700 mb-4">
                        The OpenAI API key is stored locally on the user's device using Chrome's storage API. No other data is permanently stored by the Extension.
                    </p>
                </section>

                <section class="mb-8">
                    <h2 class="text-2xl font-semibold text-[#2B3990] mb-4">5. Data Security</h2>
                    <p class="text-gray-700 mb-4">
                        We implement appropriate technical and organizational measures to protect your data against unauthorized or unlawful processing, accidental loss, destruction, or damage.
                    </p>
                </section>

                <section class="mb-8">
                    <h2 class="text-2xl font-semibold text-[#2B3990] mb-4">6. User Rights</h2>
                    <p class="text-gray-700 mb-4">
                        Users can delete their stored API key at any time through the Extension's interface.
                    </p>
                </section>

                <section class="mb-8">
                    <h2 class="text-2xl font-semibold text-[#2B3990] mb-4">7. Changes to This Policy</h2>
                    <p class="text-gray-700 mb-4">
                        We may update this privacy policy from time to time. We will notify users of any changes by posting the new privacy policy on this page.
                    </p>
                </section>

                <section class="mb-8">
                    <h2 class="text-2xl font-semibold text-[#2B3990] mb-4">8. Contact Us</h2>
                    <p class="text-gray-700 mb-4">
                        If you have any questions about this privacy policy, please contact us at:<br>
                        <a href="mailto:<EMAIL>" class="text-[#2B3990] hover:text-[#EE3124]"><EMAIL></a>
                    </p>
                </section>
            </div>
        </div>
    </div>

    <!-- Modern Footer -->
    <footer class="modern-footer py-8 mt-20">
        <div class="container mx-auto px-4">
            <div class="flex flex-col justify-center items-center space-y-4">
                <!-- Copyright text -->
                <p class="text-white text-center opacity-80">
                    © 2024 AI LABS. All rights reserved.
                </p>
                <!-- GitHub link -->
                <a href="https://github.com/ericxyz86/static_landng_page" 
                   target="_blank" 
                   rel="noopener noreferrer" 
                   class="text-white hover:text-yellow-300 transition-colors duration-300">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path fill-rule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clip-rule="evenodd"/>
                    </svg>
                </a>
            </div>
        </div>
    </footer>

    <script src="static/js/main.js"></script>
    <script src="static/js/shader-background.js"></script>
</body>
</html>
